<!DOCTYPE html>
<html lang="en">
<head>
 <!-- REPLACE the existing viewport meta tag in ALL your HTML files with this enhanced version -->

<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes, viewport-fit=cover" />
<meta name="theme-color" content="#4285a6" />
<meta name="apple-mobile-web-app-capable" content="yes" />
<meta name="apple-mobile-web-app-status-bar-style" content="default" />
<meta name="mobile-web-app-capable" content="yes" />

<!-- Additional meta tags for better mobile support -->
<meta name="format-detection" content="telephone=no" />
<meta name="msapplication-tap-highlight" content="no" />
<meta name="apple-touch-fullscreen" content="yes" />

<!-- Prevent iOS from changing colors -->
<meta name="apple-mobile-web-app-title" content="Cornish Birds of Prey" />
<meta name="application-name" content="Cornish Birds of Prey" />

<!-- Ensure proper rendering on Windows Phone -->
<meta name="msapplication-TileColor" content="#4285a6" />
<meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <title>Staff & Volunteers Report – Cornish Birds of Prey</title>
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="styles.css" />
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 1.5rem;
      background: #4285a6;
      background: linear-gradient(47deg, rgb(127, 186, 215) 0%, rgb(162, 210, 163) 59%, rgb(253, 246, 181) 100%);
      background-attachment: fixed;
      min-height: 100vh;
      color: #333;
      position: relative;
    }

    .report-container {
      max-width: 1200px;
      margin: 0 auto;
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(15px);
      border-radius: 20px;
      border: 2px solid rgba(255, 255, 255, 0.3);
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      padding: 30px;
    }

    .report-header {
      text-align: center;
      margin-bottom: 30px;
      border-bottom: 2px solid rgba(66, 133, 166, 0.3);
      padding-bottom: 20px;
    }

    .report-title {
      font-size: 2.5rem;
      color: #2c3e50;
      margin-bottom: 10px;
      font-weight: 700;
    }

    .report-subtitle {
      font-size: 1.2rem;
      color: #7f8c8d;
      margin-bottom: 10px;
    }

    .summary-section {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      margin-bottom: 30px;
    }

    .summary-card {
      background: rgba(255, 255, 255, 0.6);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 15px;
      padding: 20px;
      text-align: center;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .summary-number {
      font-size: 2.5rem;
      font-weight: bold;
      color: #2980b9;
      margin-bottom: 5px;
    }

    .summary-label {
      font-size: 0.9rem;
      color: #7f8c8d;
      text-transform: uppercase;
      letter-spacing: 1px;
    }

    .staff-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 20px;
      background: rgba(255, 255, 255, 0.8);
      border-radius: 10px;
      overflow: hidden;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .staff-table th {
      background: #8bbccf;
      color: #2c3e50;
      padding: 15px 10px;
      text-align: center;
      font-weight: 600;
      font-size: 0.9rem;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      border-right: 1px solid rgba(255, 255, 255, 0.3);
    }

    .staff-table th:last-child {
      border-right: none;
    }

    .staff-table td {
      padding: 14px 10px;
      text-align: center;
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);
      border-right: 1px solid rgba(0, 0, 0, 0.1);
      font-size: 0.9rem;
    }

    .staff-table td:last-child {
      border-right: none;
    }

    .staff-table tbody tr:hover {
      background: rgba(66, 133, 166, 0.1);
    }



    .staff-name {
      font-weight: 600;
      color: #2c3e50;
      text-align: left !important;
      padding-left: 15px !important;
    }

    .status-active {
      color: #27ae60;
      font-weight: bold;
    }

    .status-inactive {
      color: #e74c3c;
      font-weight: bold;
    }

    .role-staff {
      background: #3498db;
      color: white;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 0.8rem;
      font-weight: 500;
    }

    .role-volunteer {
      background: #9b59b6;
      color: white;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 0.8rem;
      font-weight: 500;
    }

    .date-cell {
      font-size: 0.8rem;
      color: #7f8c8d;
    }

    .floating-buttons {
      position: fixed;
      top: 20px;
      right: 20px;
      display: flex;
      flex-direction: column;
      gap: 10px;
      z-index: 1000;
    }

    .floating-btn {
      background: rgba(66, 133, 166, 0.9);
      color: white;
      border: none;
      border-radius: 25px;
      padding: 12px 20px;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 0.9rem;
      font-weight: 500;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
      transition: all 0.3s ease;
    }

    .floating-btn:hover {
      background: rgba(66, 133, 166, 1);
      transform: translateY(-2px);
    }

    .close-btn {
      background: rgba(66, 133, 166, 0.9);
    }

    .close-btn:hover {
      background: rgba(66, 133, 166, 1);
      transform: translateY(-2px);
    }

    .no-data {
      text-align: center;
      color: #666;
      font-style: italic;
      padding: 2rem;
    }

    .loading {
      text-align: center;
      color: #666;
      padding: 2rem;
    }

    /* Print styles */
    @media print {
      .floating-buttons {
        display: none !important;
      }

      body {
        background: white !important;
        padding: 0 !important;
      }

      .report-container {
        background: white !important;
        backdrop-filter: none !important;
        border: none !important;
        box-shadow: none !important;
        padding: 20px !important;
      }

      .summary-card {
        background: white !important;
        backdrop-filter: none !important;
        border: 1px solid #ddd !important;
      }

      .staff-table {
        background: white !important;
      }
    }

    /* Responsive Design */

    /* Mobile (320px - 768px) */
    @media (max-width: 768px) {
      body {
        padding: 1rem;
      }

      .report-container {
        padding: 15px;
        margin: 0;
        border-radius: 15px;
      }

      .report-title {
        font-size: 1.8rem;
      }

      .report-subtitle {
        font-size: 1rem;
      }

      .summary-section {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
      }

      .summary-card {
        padding: 15px;
      }

      .summary-number {
        font-size: 2rem;
      }

      .summary-label {
        font-size: 0.8rem;
      }

      .staff-table {
        font-size: 0.75rem;
        display: block;
        overflow-x: auto;
        white-space: nowrap;
      }

      .staff-table th,
      .staff-table td {
        padding: 8px 4px;
        min-width: 80px;
      }

      .staff-name {
        min-width: 100px !important;
        padding-left: 8px !important;
      }

      .floating-buttons {
        position: static;
        flex-direction: row;
        justify-content: center;
        margin-bottom: 20px;
        gap: 8px;
      }

      .floating-btn {
        padding: 10px 15px;
        font-size: 0.8rem;
      }
    }

    /* Tablet Portrait (768px - 1024px) */
    @media (min-width: 768px) and (max-width: 1024px) {
      .report-container {
        padding: 20px;
      }

      .report-title {
        font-size: 2.2rem;
      }

      .summary-section {
        grid-template-columns: repeat(3, 1fr);
        gap: 18px;
      }

      .summary-card {
        padding: 18px;
      }

      .summary-number {
        font-size: 2.2rem;
      }

      .staff-table {
        font-size: 0.85rem;
      }

      .staff-table th,
      .staff-table td {
        padding: 10px 8px;
      }

      .floating-buttons {
        top: 15px;
        right: 15px;
      }

      .floating-btn {
        padding: 11px 18px;
        font-size: 0.85rem;
      }
    }

    /* Tablet Landscape (1024px - 1200px) */
    @media (min-width: 1024px) and (max-width: 1200px) {
      .report-container {
        padding: 25px;
      }

      .summary-section {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
      }

      .staff-table {
        font-size: 0.9rem;
      }
    }

    /* Small Mobile (320px - 480px) */
    @media (max-width: 480px) {
      body {
        padding: 0.5rem;
      }

      .report-container {
        padding: 10px;
        border-radius: 10px;
      }

      .report-title {
        font-size: 1.5rem;
      }

      .report-subtitle {
        font-size: 0.9rem;
      }

      .summary-section {
        grid-template-columns: 1fr;
        gap: 10px;
      }

      .summary-card {
        padding: 12px;
      }

      .summary-number {
        font-size: 1.8rem;
      }

      .summary-label {
        font-size: 0.75rem;
      }

      .staff-table {
        font-size: 0.7rem;
      }

      .staff-table th,
      .staff-table td {
        padding: 6px 3px;
        min-width: 70px;
      }

      .staff-name {
        min-width: 90px !important;
        padding-left: 6px !important;
      }

      .floating-buttons {
        flex-direction: column;
        gap: 5px;
      }

      .floating-btn {
        padding: 8px 12px;
        font-size: 0.75rem;
      }
    }
  </style>
</head>
<body>
  <!-- Floating Action Buttons -->
  <div class="floating-buttons">
    <button class="floating-btn" onclick="window.print()">
      <span class="material-icons">print</span>
      Print Report
    </button>
    <button class="floating-btn" onclick="window.print()">
      <span class="material-icons">picture_as_pdf</span>
      Save as PDF
    </button>
    <button class="floating-btn close-btn" onclick="closeWindow()">
      <span class="material-icons">close</span>
      Close Window
    </button>
  </div>

  <div class="report-container">
    <div class="report-header">
      <h1 class="report-title">Staff & Volunteers Report</h1>
      <p class="report-subtitle" id="report-subtitle">Loading...</p>
    </div>

    <div class="summary-section" id="summary-section">
      <div class="summary-card">
        <div class="summary-number" id="total-people">-</div>
        <div class="summary-label">Total People</div>
      </div>
      <div class="summary-card">
        <div class="summary-number" id="active-staff">-</div>
        <div class="summary-label">Active Staff</div>
      </div>
      <div class="summary-card">
        <div class="summary-number" id="active-volunteers">-</div>
        <div class="summary-label">Active Volunteers</div>
      </div>
      <div class="summary-card">
        <div class="summary-number" id="inactive-count">-</div>
        <div class="summary-label">Inactive</div>
      </div>
    </div>

    <div id="staff-content">
      <div class="loading">Loading staff and volunteer data...</div>
    </div>
  </div>

  <script type="module">
    import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';

    const supabase = createClient(
      'https://wkclogfpyykwgjhhshsi.supabase.co',
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndrY2xvZ2ZweXlrd2dqaGhzaHNpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExMzc4OTQsImV4cCI6MjA2NjcxMzg5NH0.2SEQj0Gm8gZLZELn28JIipJiBkoxHelxWSqIxVYcq-k'
    );

    // Parse URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const filter = urlParams.get('filter') || 'all';
    const startDate = urlParams.get('startDate');
    const endDate = urlParams.get('endDate');

    // Initialize report
    async function initializeReport() {
      if (!startDate || !endDate) {
        document.getElementById('staff-content').innerHTML = '<div class="no-data">Invalid date range provided.</div>';
        return;
      }

      // Update report subtitle
      const filterText = getFilterText(filter);
      document.getElementById('report-subtitle').textContent = `${filterText} - ${startDate} to ${endDate}`;

      // Load staff and volunteer data
      await loadStaffData();
    }

    // Get filter text for subtitle
    function getFilterText(filter) {
      switch(filter) {
        case 'current-staff': return 'Current Staff Only';
        case 'current-volunteers': return 'Current Volunteers Only';
        case 'historical': return 'Historical Data';
        default: return 'All Staff & Volunteers';
      }
    }

    // Load staff and volunteer data
    async function loadStaffData() {
      let query = supabase
        .from('staff_volunteers')
        .select('*')
        .gte('created_at', startDate + 'T00:00:00')
        .lte('created_at', endDate + 'T23:59:59')
        .order('created_at', { ascending: false });

      // Apply filters
      switch(filter) {
        case 'current-staff':
          query = query.eq('role', 'Staff').eq('is_active', true);
          break;
        case 'current-volunteers':
          query = query.eq('role', 'Volunteer').eq('is_active', true);
          break;
        case 'historical':
          query = query.eq('is_active', false);
          break;
        // 'all' doesn't need additional filtering
      }

      const { data, error } = await query;

      console.log('Staff data query result:', { data, error, filter, startDate, endDate });

      if (error) {
        console.error('Error loading staff data:', error);
        document.getElementById('staff-content').innerHTML = '<div class="no-data">Error loading staff and volunteer data.</div>';
        return;
      }

      if (!data || data.length === 0) {
        console.log('No data found with current filters. Trying without date filter...');

        // Try loading all data to see if there are records
        const { data: allData, error: allError } = await supabase
          .from('staff_volunteers')
          .select('*')
          .order('created_at', { ascending: false });

        console.log('All staff data (no filters):', { data: allData, error: allError });

        document.getElementById('staff-content').innerHTML = '<div class="no-data">No staff or volunteer records found for this period and filter.</div>';
        return;
      }

      // Calculate summary statistics
      calculateSummaryStats(data);

      // Generate staff table
      await generateStaffGrid(data);
    }

    // Calculate summary statistics
    function calculateSummaryStats(data) {
      const totalPeople = data.length;

      // Use the same status logic as in the table
      const getPersonStatus = (person) => {
        if (person.status) {
          return person.status === 'Active';
        } else if (person.is_active !== undefined) {
          return person.is_active;
        }
        return false;
      };

      const activeStaff = data.filter(person => person.role === 'Staff' && getPersonStatus(person)).length;
      const activeVolunteers = data.filter(person => person.role === 'Volunteer' && getPersonStatus(person)).length;
      const inactiveCount = data.filter(person => !getPersonStatus(person)).length;

      document.getElementById('total-people').textContent = totalPeople;
      document.getElementById('active-staff').textContent = activeStaff;
      document.getElementById('active-volunteers').textContent = activeVolunteers;
      document.getElementById('inactive-count').textContent = inactiveCount;
    }

    // Generate staff table
    async function generateStaffGrid(data) {
      let tableHTML = `
        <table class="staff-table">
          <thead>
            <tr>
              <th>Name</th>
              <th>Role</th>
              <th>Position</th>
              <th>Status</th>
              <th>Added</th>
              <th>Last Login</th>
            </tr>
          </thead>
          <tbody>
      `;

      for (const person of data) {
        // Format dates
        const addedDate = person.created_at ? new Date(person.created_at).toLocaleDateString('en-GB') : 'Not recorded';
        const lastLogin = person.last_login || person.last_login_at ?
          new Date(person.last_login || person.last_login_at).toLocaleDateString('en-GB') : 'Never';

        // Determine status - check both status field and is_active field
        // Priority: status field first, then is_active field
        let isActive = false;
        if (person.status) {
          isActive = person.status === 'Active';
        } else if (person.is_active !== undefined) {
          isActive = person.is_active;
        }

        const statusClass = isActive ? 'status-active' : 'status-inactive';
        const statusText = isActive ? 'Active' : 'Inactive';
        const roleClass = person.role === 'Staff' ? 'role-staff' : 'role-volunteer';

        tableHTML += `
          <tr>
            <td class="staff-name">${person.name}</td>
            <td><span class="${roleClass}">${person.role}</span></td>
            <td>${person.position || 'No Position Listed'}</td>
            <td class="${statusClass}">${statusText}</td>
            <td class="date-cell">${addedDate}</td>
            <td class="date-cell">${lastLogin}</td>
          </tr>
        `;
      }

      tableHTML += '</tbody></table>';

      document.getElementById('staff-content').innerHTML = tableHTML;
    }

    // Initialize the report
    initializeReport();

    // Close window function - make it globally accessible
    function closeWindow() {
      window.location.href = 'reports.html';
    }

    // Make closeWindow globally accessible
    window.closeWindow = closeWindow;
  </script>
</body>
</html>
