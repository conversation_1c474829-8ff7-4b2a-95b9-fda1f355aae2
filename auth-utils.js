// Authentication Utilities for Cornish Birds of Prey Center
// This file provides authentication functions that can be used across all pages

// Supabase configuration
const SUPABASE_URL = 'https://wkclogfpyykwgjhhshsi.supabase.co';
const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndrY2xvZ2ZweXlrd2dqaGhzaHNpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExMzc4OTQsImV4cCI6MjA2NjcxMzg5NH0.2SEQj0Gm8gZLZELn28JIipJiBkoxHelxWSqIxVYcq-k';

// Global authentication state
window.authState = {
  user: null,
  profile: null,
  role: null,
  isAuthenticated: false,
  sessionTimeout: null
};

// Initialize Supabase client (will be set when imported)
let supabase = null;

// Initialize authentication system
async function initAuth() {
  try {
    // Use global Supabase client
    supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_KEY);
    
    // Check for existing session
    const { data: { session } } = await supabase.auth.getSession();
    
    if (session) {
      await setAuthenticatedUser(session.user);
      setupAutoLogout();
      return true;
    } else {
      // Redirect to login if not authenticated
      if (!window.location.pathname.includes('index.html')) {
        window.location.href = 'index.html';
      }
      return false;
    }
  } catch (error) {
    console.error('Auth initialization error:', error);
    return false;
  }
}

// Set authenticated user and load profile
async function setAuthenticatedUser(user) {
  try {
    window.authState.user = user;
    window.authState.isAuthenticated = true;
    
    // Load user profile from staff_volunteers table
    const { data, error } = await supabase
      .from('staff_volunteers')
      .select('*')
      .eq('user_id', user.id)
      .single();

    if (error) {
      throw error;
    }

    window.authState.profile = data;
    window.authState.role = data.user_role;
    
    // Update last activity
    await updateLastActivity();
    
    // Update UI with user info
    updateUserUI();
    
    return true;
  } catch (error) {
    console.error('Error loading user profile:', error);
    await logout();
    return false;
  }
}

// Update last activity timestamp
async function updateLastActivity() {
  try {
    await supabase
      .from('staff_volunteers')
      .update({ 
        last_login_at: new Date().toISOString()
      })
      .eq('user_id', window.authState.user.id);
  } catch (error) {
    console.error('Error updating last activity:', error);
  }
}

// Setup auto-logout functionality
function setupAutoLogout() {
  const TIMEOUT_MINUTES = 10;
  const TIMEOUT_MS = TIMEOUT_MINUTES * 60 * 1000;

  function resetTimeout() {
    clearTimeout(window.authState.sessionTimeout);
    window.authState.sessionTimeout = setTimeout(() => {
      logout(true); // auto logout
    }, TIMEOUT_MS);
  }

  // Reset timeout on user activity
  ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'].forEach(event => {
    document.addEventListener(event, resetTimeout, true);
  });

  resetTimeout(); // Initial setup
}

// Logout function
async function logout(isAutoLogout = false) {
  try {
    // Clear timeout
    if (window.authState.sessionTimeout) {
      clearTimeout(window.authState.sessionTimeout);
    }
    
    // Sign out from Supabase
    await supabase.auth.signOut();
    
    // Clear auth state
    window.authState = {
      user: null,
      profile: null,
      role: null,
      isAuthenticated: false,
      sessionTimeout: null
    };
    
    // Clear local storage (but preserve remember me data unless explicitly logging out)
    if (!isAutoLogout) {
      const shouldRemember = localStorage.getItem('rememberUser') === 'true';
      if (!shouldRemember) {
        localStorage.removeItem('rememberUser');
        localStorage.removeItem('savedEmail');
      }
    }
    localStorage.removeItem('biometricEnabled');
    
    if (isAutoLogout) {
      alert('You have been automatically logged out due to inactivity.');
    }
    
    // Redirect to login
    window.location.href = 'index.html';
    
  } catch (error) {
    console.error('Logout error:', error);
    // Force redirect even if logout fails
    window.location.href = 'index.html';
  }
}

// Check if user has specific permission
function hasPermission(action, resource) {
  if (!window.authState.isAuthenticated || !window.authState.role) {
    return false;
  }
  
  const role = window.authState.role;
  
  // Admin has all permissions
  if (role === 'Admin') {
    return true;
  }
  
  // Define permission matrix
  const permissions = {
    'Staff': {
      'animals': ['create', 'read', 'update'],
      'medical_episodes': ['create', 'read', 'update'],
      'medical_interventions': ['create', 'read', 'update'],
      'documents': ['create', 'read', 'update'],
      'staff_volunteers': ['read'],
      'emergency_contacts': ['read']
    },
    'Volunteer': {
      'animals': ['read', 'log'], // can view and add logs
      'medical_episodes': ['read'], // can only view open episodes
      'medical_interventions': ['read'],
      'documents': ['read'],
      'emergency_contacts': ['read']
    }
  };
  
  const rolePermissions = permissions[role];
  if (!rolePermissions || !rolePermissions[resource]) {
    return false;
  }
  
  return rolePermissions[resource].includes(action);
}

// Check if user can delete (only admins)
function canDelete() {
  return window.authState.role === 'Admin';
}

// Update UI elements based on user role
function updateUserUI() {
  const user = window.authState.user;
  const profile = window.authState.profile;
  const role = window.authState.role;
  
  // Update user display in top bar (if element exists)
  const userDisplay = document.getElementById('user-display');
  if (userDisplay) {
    userDisplay.innerHTML = `
      <div class="user-info">
        <span class="user-name">${profile.name}</span>
        <span class="user-role">${role}</span>
      </div>
      <div class="user-actions">
        <button class="security-btn" onclick="window.location.href='mfa-setup.html'" title="Security Settings">
          <span class="material-icons">security</span>
        </button>
        <button class="logout-btn" onclick="logout()">
          <span class="material-icons">logout</span>
        </button>
      </div>
    `;
  }
  
  // Hide/show elements based on permissions
  hideElementsBasedOnRole();
}

// Hide UI elements based on user role
function hideElementsBasedOnRole() {
  const role = window.authState.role;
  
  // Hide delete buttons for non-admins
  if (role !== 'Admin') {
    const deleteButtons = document.querySelectorAll('.delete-btn, [data-action="delete"]');
    deleteButtons.forEach(btn => {
      btn.style.display = 'none';
    });
  }
  
  // Hide staff/volunteer module for volunteers
  if (role === 'Volunteer') {
    const staffLinks = document.querySelectorAll('a[href="staff-volunteers.html"]');
    staffLinks.forEach(link => {
      link.style.display = 'none';
    });
  }
  
  // Hide admin-only elements
  const adminElements = document.querySelectorAll('[data-role="admin"]');
  adminElements.forEach(element => {
    if (role !== 'Admin') {
      element.style.display = 'none';
    }
  });
  
  // Hide staff-only elements
  const staffElements = document.querySelectorAll('[data-role="staff"]');
  staffElements.forEach(element => {
    if (!['Admin', 'Staff'].includes(role)) {
      element.style.display = 'none';
    }
  });
}

// Log user action for audit trail
async function logUserAction(action, tableName, recordId = null, oldValues = null, newValues = null) {
  try {
    await supabase
      .from('audit_log')
      .insert({
        user_id: window.authState.user.id,
        action: action,
        table_name: tableName,
        record_id: recordId,
        old_values: oldValues,
        new_values: newValues,
        ip_address: null, // Would need additional setup to capture
        user_agent: navigator.userAgent
      });
  } catch (error) {
    console.error('Error logging user action:', error);
  }
}

// Add user attribution to form data
function addUserAttribution(formData, isUpdate = false) {
  const userId = window.authState.user?.id;
  if (!userId) return formData;

  if (isUpdate) {
    formData.updated_by = userId;
  } else {
    formData.created_by = userId;
  }

  return formData;
}

// Get current user display name and role for forms
function getCurrentUserInfo() {
  if (!window.authState.isAuthenticated || !window.authState.profile) {
    return null;
  }

  return {
    name: window.authState.profile.name,
    role: window.authState.role,
    userId: window.authState.user.id
  };
}

// Add user attribution display to a form
function addUserAttributionToForm(formId) {
  const form = document.getElementById(formId);
  if (!form) return;

  const userInfo = getCurrentUserInfo();
  if (!userInfo) return;

  // Check if attribution already exists
  const existingAttribution = form.querySelector('.user-attribution');
  if (existingAttribution) {
    existingAttribution.remove();
  }

  // Create user attribution display
  const attributionDiv = document.createElement('div');
  attributionDiv.className = 'form-group user-attribution';
  attributionDiv.innerHTML = `
    <label>Filled in by:</label>
    <input type="text" value="${userInfo.name} (${userInfo.role})" readonly class="readonly-field">
    <input type="hidden" name="created_by" value="${userInfo.userId}">
  `;

  // Insert at the end of the form, before form-actions div or submit button
  const formActions = form.querySelector('.form-actions');
  const submitButton = form.querySelector('button[type="submit"]');

  if (formActions) {
    // Insert before the form-actions div
    form.insertBefore(attributionDiv, formActions);
  } else if (submitButton && submitButton.parentNode === form) {
    // Insert before submit button only if it's a direct child of the form
    form.insertBefore(attributionDiv, submitButton);
  } else {
    // Fallback: append to the end of the form
    form.appendChild(attributionDiv);
  }
}

// Format user name for display (handles cases where user might be deleted)
function formatUserName(createdBy, updatedBy, createdByName = null, updatedByName = null) {
  const currentUser = getCurrentUserInfo();

  let createdByDisplay = 'Unknown User';
  let updatedByDisplay = 'Unknown User';

  if (createdBy) {
    if (createdByName) {
      createdByDisplay = createdByName;
    } else if (currentUser && currentUser.userId === createdBy) {
      createdByDisplay = `${currentUser.name} (You)`;
    } else {
      createdByDisplay = 'Unknown User';
    }
  }

  if (updatedBy) {
    if (updatedByName) {
      updatedByDisplay = updatedByName;
    } else if (currentUser && currentUser.userId === updatedBy) {
      updatedByDisplay = `${currentUser.name} (You)`;
    } else {
      updatedByDisplay = 'Unknown User';
    }
  }

  return { createdByDisplay, updatedByDisplay };
}

// Check if current user can access medical episode (volunteers can only see open episodes)
function canAccessMedicalEpisode(episode) {
  const role = window.authState.role;
  
  if (role === 'Admin' || role === 'Staff') {
    return true;
  }
  
  if (role === 'Volunteer') {
    return episode.status !== 'resolved';
  }
  
  return false;
}

// Initialize authentication when script loads
document.addEventListener('DOMContentLoaded', function() {
  // Only initialize auth if not on login page
  if (!window.location.pathname.includes('index.html')) {
    initAuth();
  }
});

// Make functions globally available
window.authUtils = {
  initAuth,
  setAuthenticatedUser,
  logout,
  hasPermission,
  canDelete,
  updateUserUI,
  logUserAction,
  addUserAttribution,
  getCurrentUserInfo,
  addUserAttributionToForm,
  formatUserName,
  canAccessMedicalEpisode,
  supabase: () => supabase
};

// Functions are available globally via window.authUtils object
