/**
 * Modal Helper Functions for Consistent Modal Positioning and Animation
 * Cornish Birds of Prey Center Application
 */

/**
 * Opens a modal with specified size and smooth animation
 * @param {string} modalId - The ID of the modal to open
 * @param {string} size - Size class: 'large', 'medium', 'small', or 'xs'
 */
function openModal(modalId, size = 'medium') {
  const modal = document.getElementById(modalId);
  if (!modal) {
    console.error(`Modal with ID '${modalId}' not found`);
    return;
  }
  
  const modalContent = modal.querySelector('.modal-content');
  if (!modalContent) {
    console.error(`Modal content not found in modal '${modalId}'`);
    return;
  }
  
  // Remove existing size classes and add new one
  modalContent.className = modalContent.className.replace(/\b(large|medium|small|xs)\b/g, '');
  modalContent.classList.add(size);
  
  // Show modal with animation
  modal.classList.add('show');
  modal.style.display = 'flex';
  
  // Focus first input after animation completes
  setTimeout(() => {
    const firstInput = modal.querySelector('input, textarea, select');
    if (firstInput && !firstInput.disabled) {
      firstInput.focus();
    }
  }, 300);
  
  // Add escape key listener
  document.addEventListener('keydown', handleEscapeKey);
}

/**
 * Closes a modal with smooth animation
 * @param {string} modalId - The ID of the modal to close
 */
function closeModal(modalId) {
  const modal = document.getElementById(modalId);
  if (!modal) {
    console.error(`Modal with ID '${modalId}' not found`);
    return;
  }
  
  const modalContent = modal.querySelector('.modal-content');
  if (!modalContent) {
    console.error(`Modal content not found in modal '${modalId}'`);
    return;
  }
  
  // Start closing animation
  modalContent.style.transform = 'scale(0.9)';
  modalContent.style.opacity = '0';
  
  // Complete close after animation
  setTimeout(() => {
    modal.classList.remove('show');
    modal.style.display = 'none';
    modalContent.style.transform = '';
    modalContent.style.opacity = '';
  }, 300);
  
  // Remove escape key listener
  document.removeEventListener('keydown', handleEscapeKey);
}

/**
 * Handles escape key press to close modals
 * @param {KeyboardEvent} event - The keyboard event
 */
function handleEscapeKey(event) {
  if (event.key === 'Escape') {
    // Find the currently open modal
    const openModal = document.querySelector('.modal.show');
    if (openModal) {
      closeModal(openModal.id);
    }
  }
}

/**
 * Closes modal when clicking outside the modal content
 * @param {Event} event - The click event
 * @param {string} modalId - The ID of the modal
 */
function closeModalOnOutsideClick(event, modalId) {
  const modal = document.getElementById(modalId);
  const modalContent = modal.querySelector('.modal-content');
  
  if (event.target === modal && !modalContent.contains(event.target)) {
    closeModal(modalId);
  }
}

/**
 * Initialize modal event listeners for all modals
 */
function initializeModals() {
  // Get all modals
  const modals = document.querySelectorAll('.modal, [id$="-modal"]');
  
  modals.forEach(modal => {
    // Add click outside to close
    modal.addEventListener('click', (event) => {
      closeModalOnOutsideClick(event, modal.id);
    });
    
    // Add close button functionality
    const closeButton = modal.querySelector('.close-modal');
    if (closeButton) {
      closeButton.addEventListener('click', () => {
        closeModal(modal.id);
      });
    }
  });
}

/**
 * Show confirmation modal
 * @param {string} message - The confirmation message
 * @param {Function} onConfirm - Callback function when confirmed
 * @param {Function} onCancel - Callback function when cancelled (optional)
 */
function showConfirmationModal(message, onConfirm, onCancel = null) {
  // Create confirmation modal if it doesn't exist
  let confirmModal = document.getElementById('confirmation-modal');
  if (!confirmModal) {
    confirmModal = createConfirmationModal();
    document.body.appendChild(confirmModal);
  }
  
  // Set message
  const messageElement = confirmModal.querySelector('.confirmation-message');
  messageElement.textContent = message;
  
  // Set up button handlers
  const confirmBtn = confirmModal.querySelector('.confirm-btn');
  const cancelBtn = confirmModal.querySelector('.cancel-btn');
  
  confirmBtn.onclick = () => {
    closeModal('confirmation-modal');
    if (onConfirm) onConfirm();
  };
  
  cancelBtn.onclick = () => {
    closeModal('confirmation-modal');
    if (onCancel) onCancel();
  };
  
  // Show modal
  openModal('confirmation-modal', 'small');
}

/**
 * Creates a confirmation modal element
 * @returns {HTMLElement} The confirmation modal element
 */
function createConfirmationModal() {
  const modal = document.createElement('div');
  modal.id = 'confirmation-modal';
  modal.className = 'modal';
  
  modal.innerHTML = `
    <div class="modal-content small">
      <span class="close-modal">&times;</span>
      <h3>Confirm Action</h3>
      <p class="confirmation-message"></p>
      <div class="form-actions">
        <button type="button" class="btn-secondary cancel-btn">Cancel</button>
        <button type="button" class="btn-primary confirm-btn">Confirm</button>
      </div>
    </div>
  `;
  
  return modal;
}

/**
 * Auto-enhance modals when opened (works with existing code)
 * This ensures modals always appear in the current viewport
 */
const modalObserver = new MutationObserver(function(mutations) {
  mutations.forEach(function(mutation) {
    if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
      const modal = mutation.target;
      if (modal.classList.contains('modal') || modal.id.includes('modal')) {
        const isVisible = modal.style.display === 'block' || modal.style.display === 'flex';
        if (isVisible) {
          // Ensure proper positioning
          modal.style.position = 'fixed';
          modal.style.top = '0';
          modal.style.left = '0';
          modal.style.width = '100vw';
          modal.style.height = '100vh';
          modal.style.zIndex = '9999';
          document.body.style.overflow = 'hidden';
        } else {
          document.body.style.overflow = '';
        }
      }
    }
  });
});

/**
 * Initialize modal enhancements
 */
function initializeModalEnhancements() {
  // Observe all modals for style changes
  const modals = document.querySelectorAll('.modal, [id*="modal"]');
  modals.forEach(modal => {
    modalObserver.observe(modal, { attributes: true, attributeFilter: ['style'] });
  });
}

// Initialize modals when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
  initializeModals();
  initializeModalEnhancements();
});

// Export functions for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    openModal,
    closeModal,
    showConfirmationModal,
    initializeModals,
    initializeModalEnhancements
  };
}
