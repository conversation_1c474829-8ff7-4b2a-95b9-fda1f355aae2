<!DOCTYPE html>
<html lang="en">
<head>
 <!-- REPLACE the existing viewport meta tag in ALL your HTML files with this enhanced version -->

<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes, viewport-fit=cover" />
<meta name="theme-color" content="#4285a6" />
<meta name="apple-mobile-web-app-capable" content="yes" />
<meta name="apple-mobile-web-app-status-bar-style" content="default" />
<meta name="mobile-web-app-capable" content="yes" />

<!-- Additional meta tags for better mobile support -->
<meta name="format-detection" content="telephone=no" />
<meta name="msapplication-tap-highlight" content="no" />
<meta name="apple-touch-fullscreen" content="yes" />

<!-- Prevent iOS from changing colors -->
<meta name="apple-mobile-web-app-title" content="Cornish Birds of Prey" />
<meta name="application-name" content="Cornish Birds of Prey" />

<!-- Ensure proper rendering on Windows Phone -->
<meta name="msapplication-TileColor" content="#4285a6" />
<meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <title>Document Management - Cornish Birds of Prey</title>
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="styles.css" />
</head>
<body>
  <div class="app-container">
    <header class="top-bar">
      <button class="menu-btn" onclick="toggleMenu()">
        <span class="material-icons">menu</span>
      </button>
      <h1>Document Library</h1>
      <div class="header-actions">
        <a href="dashboard.html" class="back-link" title="Back to Dashboard">
          <span class="material-icons">arrow_back</span>
        </a>
      </div>
    </header>

    <main class="documents-main">
          <!-- Category and Date Selection -->
          <div class="document-metadata-section">
            <div class="category-selection">
              <label for="document-category">Document Category:</label>
              <select id="document-category" required>
                <option value="">Select Category</option>
                <option value="policy">Policy Documents</option>
                <option value="risk-assessment">Risk Assessments</option>
                <option value="emergency">Emergency Procedures</option>
              <option value="medical">Medical Document</option>
              <option value="other">Other/Miscellaneous</option>
              </select>
            </div>

            <div class="date-selection">
              <label for="document-created-date">Document Created On:</label>
              <input type="date" id="document-created-date" name="document-created-date">
            </div>

            <div class="date-selection">
              <label for="document-review-date">Review Document On:</label>
              <input type="date" id="document-review-date" name="document-review-date">
            </div>
          </div>

      <!-- Upload Section -->
      <section class="upload-section">
        <div class="upload-card">
          <h2>Upload New Document</h2>
          <div class="upload-area" id="upload-area">
            <div class="upload-content">
              <span class="material-icons upload-icon">cloud_upload</span>
              <p>Drag and drop files here or click to browse</p>
              <p class="file-types">Accepted: .doc, .docx, .pdf</p>
              <input type="file" id="file-input" accept=".doc,.docx,.pdf" multiple hidden>
              <button type="button" class="upload-btn" onclick="document.getElementById('file-input').click()">
                Choose Files
              </button>
            </div>
          </div>
          


          <!-- Upload Progress -->
          <div id="upload-progress" class="upload-progress" style="display: none;">
            <div class="progress-bar">
              <div class="progress-fill" id="progress-fill"></div>
            </div>
            <span id="progress-text">0%</span>
          </div>
        </div>
      </section>

      <!-- Documents Display -->
      <section class="documents-section">
        <div class="documents-header">
          <h2>Document Library</h2>
          <div class="view-toggle">
            <button class="view-btn active" data-view="grid" onclick="switchView('grid')">
              <span class="material-icons">grid_view</span>
            </button>
            <button class="view-btn" data-view="list" onclick="switchView('list')">
              <span class="material-icons">view_list</span>
            </button>
          </div>
        </div>

        <!-- Category Filters -->
        <div class="category-filters">
          <button class="filter-btn active" data-category="all" onclick="filterDocuments('all')">All Documents</button>
          <button class="filter-btn" data-category="policy" onclick="filterDocuments('policy')">Policy Documents</button>
          <button class="filter-btn" data-category="risk-assessment" onclick="filterDocuments('risk-assessment')">Risk Assessments</button>
          <button class="filter-btn" data-category="emergency" onclick="filterDocuments('emergency')">Emergency Procedures</button>
        </div>

        <!-- Documents Grid/List -->
        <div id="documents-container" class="documents-grid">
          <!-- Documents will be loaded here -->
        </div>

        <!-- Empty State -->
        <div id="empty-state" class="empty-state" style="display: none;">
          <span class="material-icons">description</span>
          <h3>No documents found</h3>
          <p>Upload your first document to get started</p>
        </div>
      </section>
    </main>
  </div>

  <!-- Delete Confirmation Modal -->
  <div id="delete-modal" class="modal">
    <div class="modal-content">
      <span class="close-modal" onclick="closeDeleteModal()">✖</span>
      <h2>Delete Document</h2>
      <p>Are you sure you want to delete this document? This action cannot be undone.</p>
      <div class="modal-actions">
        <button type="button" class="cancel-btn" onclick="closeDeleteModal()">Cancel</button>
        <button type="button" class="delete-btn" onclick="confirmDelete()">Delete</button>
      </div>
    </div>
  </div>

  <!-- Side Menu -->
  <nav id="side-menu" class="side-menu">
    <div class="menu-header">
      <h2>Cornish Birds of Prey</h2>
      <button class="close-menu" onclick="toggleMenu()">
        <span class="material-icons">close</span>
      </button>
    </div>
    <ul class="menu-items">
      <li><a href="dashboard.html"><span class="material-icons">dashboard</span> Dashboard</a></li>
      <li><a href="animal-index.html"><span class="material-icons">pets</span> Animals</a></li>
      <li><a href="staff-volunteers.html"><span class="material-icons">people</span> Staff & Volunteers</a></li>
      <li><a href="donations-adoptions.html"><span class="material-icons">volunteer_activism</span> Adoptions & Donations</a></li>
      <li class="active"><a href="documents.html"><span class="material-icons">description</span> Documents</a></li>
      <li><a href="reports.html"><span class="material-icons">bar_chart</span> Reports</a></li>
      <li><a href="emergency-contacts.html"><span class="material-icons">emergency</span> Emergency Contacts</a></li>
      <li class="menu-bottom">
        <a href="mfa-setup.html" class="security-item"><span class="material-icons">security</span> MFA Authentication</a>
      </li>
      <li class="menu-bottom">
        <a href="#" onclick="logout()" class="logout-item"><span class="material-icons">logout</span> Logout</a>
      </li>
    </ul>
  </nav>

  <!-- Menu Overlay -->
  <div id="menu-overlay" class="menu-overlay" onclick="toggleMenu()"></div>

  <script src="auth-utils.js"></script>
  <script>
    // Global variables
    let currentDocuments = [];
    let currentView = 'grid';
    let currentFilter = 'all';
    let documentToDelete = null;

    // Menu functionality
    function toggleMenu() {
      console.log('toggleMenu called');
      const sideMenu = document.getElementById('side-menu');
      const overlay = document.getElementById('menu-overlay');

      if (!sideMenu || !overlay) {
        console.error('Menu elements not found:', { sideMenu, overlay });
        return;
      }

      const isOpen = sideMenu.classList.contains('open');
      console.log('Menu is currently:', isOpen ? 'open' : 'closed');

      if (isOpen) {
        sideMenu.classList.remove('open');
        overlay.classList.remove('active');
        console.log('Menu closed');
      } else {
        sideMenu.classList.add('open');
        overlay.classList.add('active');
        console.log('Menu opened');
      }
    }

    // View switching
    function switchView(view) {
      currentView = view;
      document.querySelectorAll('.view-btn').forEach(btn => btn.classList.remove('active'));
      document.querySelector(`[data-view="${view}"]`).classList.add('active');
      
      const container = document.getElementById('documents-container');
      container.className = view === 'grid' ? 'documents-grid' : 'documents-list';
      
      renderDocuments();
    }

    // Category filtering
    function filterDocuments(category) {
      currentFilter = category;
      document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));
      document.querySelector(`[data-category="${category}"]`).classList.add('active');
      
      renderDocuments();
    }

    // Delete modal functions
    function openDeleteModal(documentId) {
      documentToDelete = documentId;
      document.getElementById('delete-modal').style.display = 'flex';
    }

    function closeDeleteModal() {
      documentToDelete = null;
      document.getElementById('delete-modal').style.display = 'none';
    }

    function confirmDelete() {
      if (documentToDelete) {
        deleteDocument(documentToDelete);
        closeDeleteModal();
      }
    }

    // File size formatting
    function formatFileSize(bytes) {
      if (bytes === 0) return '0 Bytes';
      const k = 1024;
      const sizes = ['Bytes', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Date formatting
    function formatDate(dateString) {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-GB', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      });
    }

    // Get category display name
    function getCategoryDisplayName(category) {
      const categoryNames = {
        'policy': 'Policy Documents',
        'risk-assessment': 'Risk Assessments',
        'emergency': 'Emergency Procedures'
      };
      return categoryNames[category] || category;
    }

    // Get file icon based on extension
    function getFileIcon(filename) {
      const extension = filename.split('.').pop().toLowerCase();
      switch (extension) {
        case 'pdf':
          return 'picture_as_pdf';
        case 'doc':
        case 'docx':
          return 'description';
        default:
          return 'insert_drive_file';
      }
    }
  </script>

  <script type="module">
    // Supabase configuration
    const SUPABASE_URL = 'https://wkclogfpyykwgjhhshsi.supabase.co';
    const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndrY2xvZ2ZweXlrd2dqaGhzaHNpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExMzc4OTQsImV4cCI6MjA2NjcxMzg5NH0.2SEQj0Gm8gZLZELn28JIipJiBkoxHelxWSqIxVYcq-k';

    // Initialize Supabase client
    import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';
    const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

    // Load documents on page load
    document.addEventListener('DOMContentLoaded', async function() {
      await ensureAuthenticated();
      loadDocuments();
      setupFileUpload();
      setupDateHandlers();
    });

    // Setup date field handlers
    function setupDateHandlers() {
      const createdDateField = document.getElementById('document-created-date');
      const reviewDateField = document.getElementById('document-review-date');

      // Auto-calculate review date when created date changes
      createdDateField.addEventListener('change', function() {
        if (this.value && !reviewDateField.value) {
          const createdDate = new Date(this.value);
          const reviewDate = new Date(createdDate);
          reviewDate.setFullYear(reviewDate.getFullYear() + 1);

          // Format date as YYYY-MM-DD for input field
          const formattedDate = reviewDate.toISOString().split('T')[0];
          reviewDateField.value = formattedDate;
        }
      });

      // Validate review date when it changes
      reviewDateField.addEventListener('change', function() {
        const createdDate = createdDateField.value;
        if (createdDate && this.value) {
          const created = new Date(createdDate);
          const review = new Date(this.value);
          const maxReviewDate = new Date(created);
          maxReviewDate.setFullYear(maxReviewDate.getFullYear() + 1);

          if (review > maxReviewDate) {
            alert('Review date cannot be more than 12 months from the document created date.');
            this.value = ''; // Clear the invalid date
          }
        }
      });
    }

    // Ensure user is authenticated (create anonymous session if needed)
    async function ensureAuthenticated() {
      try {
        const { data: { user }, error } = await supabase.auth.getUser();

        if (!user) {
          // Create an anonymous session for testing
          const { data, error: signInError } = await supabase.auth.signInAnonymously();
          if (signInError) {
            console.error('Authentication error:', signInError);
            alert('Authentication required. Please contact administrator.');
            return;
          }
          console.log('Created anonymous session for document management');
        }
      } catch (error) {
        console.error('Authentication check failed:', error);
      }
    }

    // Setup file upload functionality
    function setupFileUpload() {
      const fileInput = document.getElementById('file-input');
      const uploadArea = document.getElementById('upload-area');

      // File input change handler
      fileInput.addEventListener('change', handleFileSelect);

      // Drag and drop handlers
      uploadArea.addEventListener('dragover', handleDragOver);
      uploadArea.addEventListener('dragleave', handleDragLeave);
      uploadArea.addEventListener('drop', handleFileDrop);
    }

    function handleDragOver(e) {
      e.preventDefault();
      e.stopPropagation();
      e.currentTarget.classList.add('drag-over');
    }

    function handleDragLeave(e) {
      e.preventDefault();
      e.stopPropagation();
      e.currentTarget.classList.remove('drag-over');
    }

    function handleFileDrop(e) {
      e.preventDefault();
      e.stopPropagation();
      e.currentTarget.classList.remove('drag-over');

      const files = Array.from(e.dataTransfer.files);
      processFiles(files);
    }

    function handleFileSelect(e) {
      const files = Array.from(e.target.files);
      processFiles(files);
    }

    function processFiles(files) {
      const category = document.getElementById('document-category').value;
      const createdDate = document.getElementById('document-created-date').value;
      const reviewDate = document.getElementById('document-review-date').value;

      if (!category) {
        alert('Please select a document category before uploading files.');
        return;
      }

      // Validate review date if both dates are provided
      if (createdDate && reviewDate) {
        const created = new Date(createdDate);
        const review = new Date(reviewDate);
        const maxReviewDate = new Date(created);
        maxReviewDate.setFullYear(maxReviewDate.getFullYear() + 1);

        if (review > maxReviewDate) {
          alert('Review date cannot be more than 12 months from the document created date.');
          return;
        }
      }

      // Validate file types
      const validExtensions = ['.doc', '.docx', '.pdf'];
      const invalidFiles = files.filter(file => {
        const extension = '.' + file.name.split('.').pop().toLowerCase();
        return !validExtensions.includes(extension);
      });

      if (invalidFiles.length > 0) {
        alert(`Invalid file types detected. Only .doc, .docx, and .pdf files are allowed.\nInvalid files: ${invalidFiles.map(f => f.name).join(', ')}`);
        return;
      }

      // Upload files
      files.forEach(file => uploadFile(file, category, createdDate, reviewDate));
    }

    async function uploadFile(file, category, createdDate, reviewDate) {
      try {
        showUploadProgress();

        // Check if user is authenticated
        const { data: { user }, error: authError } = await supabase.auth.getUser();
        if (authError || !user) {
          throw new Error('You must be logged in to upload documents. Please refresh the page and try again.');
        }

        // Generate unique filename
        const timestamp = Date.now();
        const fileName = `${timestamp}_${file.name}`;

        // Upload to Supabase storage
        const { data: uploadData, error: uploadError } = await supabase.storage
          .from('documents')
          .upload(fileName, file, {
            onUploadProgress: (progress) => {
              const percentage = Math.round((progress.loaded / progress.total) * 100);
              updateUploadProgress(percentage);
            }
          });

        if (uploadError) {
          console.error('Storage upload error:', uploadError);
          throw new Error(`Upload failed: ${uploadError.message}`);
        }

        // Get public URL
        const { data: { publicUrl } } = supabase.storage
          .from('documents')
          .getPublicUrl(fileName);

        // Calculate dates if not provided
        let finalCreatedDate = createdDate;
        let finalReviewDate = reviewDate;

        // If no created date provided, use current date
        if (!finalCreatedDate) {
          finalCreatedDate = new Date().toISOString().split('T')[0];
        }

        // If no review date provided, set to 12 months from created date
        if (!finalReviewDate) {
          const baseDate = new Date(finalCreatedDate);
          baseDate.setFullYear(baseDate.getFullYear() + 1);
          finalReviewDate = baseDate.toISOString().split('T')[0];
        }

        // Save document metadata to database
        const documentData = {
          filename: file.name,
          original_filename: file.name,
          file_path: fileName,
          file_url: publicUrl,
          category: category,
          file_size: file.size,
          mime_type: file.type,
          uploaded_at: new Date().toISOString(),
          created_at: new Date(finalCreatedDate).toISOString(),
          review_date: finalReviewDate,
          user_id: user.id // Add user ID for RLS
        };

        const { data, error } = await supabase
          .from('documents')
          .insert([documentData])
          .select();

        if (error) {
          console.error('Database insert error:', error);
          // If database insert fails, try to clean up the uploaded file
          await supabase.storage.from('documents').remove([fileName]);
          throw new Error(`Database error: ${error.message}`);
        }

        hideUploadProgress();
        showToast('Document uploaded successfully!', 'success');
        loadDocuments();

        // Reset form
        document.getElementById('file-input').value = '';
        document.getElementById('document-category').value = '';

      } catch (error) {
        console.error('Upload error:', error);
        hideUploadProgress();
        alert('Error uploading document: ' + error.message);
      }
    }

    function showUploadProgress() {
      document.getElementById('upload-progress').style.display = 'block';
    }

    function hideUploadProgress() {
      document.getElementById('upload-progress').style.display = 'none';
      updateUploadProgress(0);
    }

    function updateUploadProgress(percentage) {
      document.getElementById('progress-fill').style.width = percentage + '%';
      document.getElementById('progress-text').textContent = percentage + '%';
    }

    // Load documents from database
    async function loadDocuments() {
      try {
        const { data: documents, error } = await supabase
          .from('documents')
          .select('*')
          .order('uploaded_at', { ascending: false });

        if (error) {
          throw error;
        }

        currentDocuments = documents || [];
        renderDocuments();

      } catch (error) {
        console.error('Error loading documents:', error);
        alert('Error loading documents: ' + error.message);
      }
    }

    // Render documents based on current view and filter
    function renderDocuments() {
      const container = document.getElementById('documents-container');
      const emptyState = document.getElementById('empty-state');

      // Filter documents
      let filteredDocuments = currentDocuments;
      if (currentFilter !== 'all') {
        filteredDocuments = currentDocuments.filter(doc => doc.category === currentFilter);
      }

      if (filteredDocuments.length === 0) {
        container.innerHTML = '';
        emptyState.style.display = 'block';
        return;
      }

      emptyState.style.display = 'none';

      if (currentView === 'grid') {
        renderGridView(filteredDocuments);
      } else {
        renderListView(filteredDocuments);
      }
    }

    function renderGridView(documents) {
      const container = document.getElementById('documents-container');
      container.innerHTML = documents.map(doc => `
        <div class="document-card" data-category="${doc.category}">
          <div class="document-category">${getCategoryDisplayName(doc.category)}</div>
          <div class="document-content">
            <div class="document-left">
              <div class="document-icon">
                <span class="material-icons">${getFileIcon(doc.filename)}</span>
              </div>
              <div class="document-info">
                <h3 class="document-title" title="${doc.filename}">${doc.filename}</h3>
              </div>
            </div>
            <div class="document-right">
              <div class="document-meta">
                <span class="upload-date">${formatDate(doc.uploaded_at)}</span>
                <span class="file-size">${formatFileSize(doc.file_size)}</span>
              </div>
            </div>
          </div>
          <div class="document-actions">
            <button class="action-btn download-btn" onclick="downloadDocument('${doc.file_url}', '${doc.filename}')" title="Download">
              <span class="material-icons">download</span>
            </button>
            <button class="action-btn delete-btn" onclick="openDeleteModal(${doc.id})" title="Delete">
              <span class="material-icons">delete</span>
            </button>
          </div>
        </div>
      `).join('');
    }

    function renderListView(documents) {
      const container = document.getElementById('documents-container');
      container.innerHTML = `
        <div class="documents-table">
          <div class="table-header">
            <div class="col-icon"></div>
            <div class="col-name">Name</div>
            <div class="col-category">Category</div>
            <div class="col-size">Size</div>
            <div class="col-date">Date</div>
            <div class="col-actions">Actions</div>
          </div>
          ${documents.map(doc => `
            <div class="table-row" data-category="${doc.category}">
              <div class="col-icon">
                <span class="material-icons">${getFileIcon(doc.filename)}</span>
              </div>
              <div class="col-name" title="${doc.filename}">${doc.filename}</div>
              <div class="col-category">${getCategoryDisplayName(doc.category)}</div>
              <div class="col-size">${formatFileSize(doc.file_size)}</div>
              <div class="col-date">${formatDate(doc.uploaded_at)}</div>
              <div class="col-actions">
                <button class="action-btn download-btn" onclick="downloadDocument('${doc.file_url}', '${doc.filename}')" title="Download">
                  <span class="material-icons">download</span>
                </button>
                <button class="action-btn delete-btn" onclick="openDeleteModal(${doc.id})" title="Delete">
                  <span class="material-icons">delete</span>
                </button>
              </div>
            </div>
          `).join('')}
        </div>
      `;
    }

    // Download document
    window.downloadDocument = function(url, filename) {
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    };

    // Delete document
    window.deleteDocument = async function(documentId) {
      try {
        // Get document info first
        const { data: document, error: fetchError } = await supabase
          .from('documents')
          .select('file_path')
          .eq('id', documentId)
          .single();

        if (fetchError) {
          throw fetchError;
        }

        // Delete from storage
        const { error: storageError } = await supabase.storage
          .from('documents')
          .remove([document.file_path]);

        if (storageError) {
          console.warn('Storage deletion error:', storageError);
        }

        // Delete from database
        const { error: dbError } = await supabase
          .from('documents')
          .delete()
          .eq('id', documentId);

        if (dbError) {
          throw dbError;
        }

        alert('Document deleted successfully!');
        loadDocuments();

      } catch (error) {
        console.error('Delete error:', error);
        alert('Error deleting document: ' + error.message);
      }
    };

    // Make functions globally available
    window.openDeleteModal = openDeleteModal;
    window.closeDeleteModal = closeDeleteModal;
    window.confirmDelete = confirmDelete;
    window.switchView = switchView;
    window.filterDocuments = filterDocuments;

    // Toast notification function
    function showToast(message, type = 'success') {
      const toast = document.getElementById('toast');
      const toastMessage = document.getElementById('toast-message');

      toastMessage.textContent = message;
      toast.className = `toast ${type} show`;

      setTimeout(() => {
        toast.classList.remove('show');
      }, 3000);
    }
  </script>

  <!-- Toast Notification -->
  <div id="toast" class="toast">
    <span id="toast-message"></span>
  </div>
</body>
</html>
