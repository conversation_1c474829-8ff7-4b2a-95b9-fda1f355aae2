<!DOCTYPE html>
<html lang="en">
<head>
  <!-- REPLACE the existing viewport meta tag in ALL your HTML files with this enhanced version -->

<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes, viewport-fit=cover" />
<meta name="theme-color" content="#4285a6" />
<meta name="apple-mobile-web-app-capable" content="yes" />
<meta name="apple-mobile-web-app-status-bar-style" content="default" />
<meta name="mobile-web-app-capable" content="yes" />

<!-- Additional meta tags for better mobile support -->
<meta name="format-detection" content="telephone=no" />
<meta name="msapplication-tap-highlight" content="no" />
<meta name="apple-touch-fullscreen" content="yes" />

<!-- Prevent iOS from changing colors -->
<meta name="apple-mobile-web-app-title" content="Cornish Birds of Prey" />
<meta name="application-name" content="Cornish Birds of Prey" />

<!-- Ensure proper rendering on Windows Phone -->
<meta name="msapplication-TileColor" content="#4285a6" />
<meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <title>Emergency Contacts - Cornish Birds of Prey</title>
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="styles.css" />
  <script src="auth-utils.js"></script>
</head>
<body>
  <div class="app-container">
    <!-- Top Navigation -->
    <header class="top-bar">
      <button class="menu-btn" onclick="toggleMenu()">
        <span class="material-icons">menu</span>
      </button>
      <h1>Emergency Contacts</h1>
      <div class="header-actions">
        <a href="dashboard.html" class="back-link" title="Back to Dashboard">
          <span class="material-icons">arrow_back</span>
        </a>
      </div>
    </header>

    <!-- Emergency Action Bar -->
    <div class="emergency-action-bar">
      <button class="emergency-btn call-999" onclick="call999()">
        <span class="material-icons">phone</span>
        Call 999
      </button>
      <button class="emergency-btn notify-owner" onclick="notifyOwner()">
        <span class="material-icons">notification_important</span>
        Notify Owner
      </button>
    </div>

    <!-- Search and Filter Section -->
    <div class="search-filter-section">
      <div class="search-container">
        <input type="text" id="search-input" class="search-input" placeholder="Search contacts..." onkeyup="filterContacts()">
      </div>
      <div class="filter-buttons">
        <button class="filter-btn active" data-priority="all" onclick="filterByPriority('all')">All</button>
        <button class="filter-btn priority-high" data-priority="high" onclick="filterByPriority('high')">High</button>
        <button class="filter-btn priority-medium" data-priority="medium" onclick="filterByPriority('medium')">Medium</button>
        <button class="filter-btn priority-low" data-priority="low" onclick="filterByPriority('low')">Low</button>
      </div>
    </div>

    <!-- Main Content -->
    <main class="emergency-contacts-main">
      <!-- Loading State -->
      <div id="loading-state" class="loading-state">
        <div class="loading-spinner"></div>
        <p>Loading emergency contacts...</p>
      </div>

      <!-- Contacts Container -->
      <div id="contacts-container" class="contacts-container" style="display: none;">
        <!-- Contacts will be dynamically inserted here -->
      </div>

      <!-- Empty State -->
      <div id="empty-state" class="empty-state" style="display: none;">
        <span class="material-icons">contact_phone</span>
        <h3>No Emergency Contacts</h3>
        <p>Add your first emergency contact to get started</p>
      </div>
    </main>

    <!-- Floating Add Button -->
    <button class="fab emergency-fab" onclick="openAddContactModal()">
      <span class="material-icons">add</span>
      Add Contact
    </button>
  </div>

  <!-- Hamburger Menu -->
  <nav id="side-menu" class="side-menu">
    <div class="menu-header">
      <h2>Cornish Birds of Prey</h2>
      <button class="close-menu" onclick="toggleMenu()">
        <span class="material-icons">close</span>
      </button>
    </div>
    <ul class="menu-items">
      <li><a href="dashboard.html"><span class="material-icons">dashboard</span> Dashboard</a></li>
      <li><a href="index.html"><span class="material-icons">pets</span> Animals</a></li>
      <li><a href="staff-volunteers.html"><span class="material-icons">people</span> Staff & Volunteers</a></li>
      <li><a href="donations-adoptions.html"><span class="material-icons">volunteer_activism</span> Adoptions & Donations</a></li>
      <li><a href="documents.html"><span class="material-icons">description</span> Documents</a></li>
      <li><a href="reports.html"><span class="material-icons">bar_chart</span> Reports</a></li>
      <li class="active"><a href="emergency-contacts.html"><span class="material-icons">emergency</span> Emergency Contacts</a></li>
      <li class="menu-bottom">
        <a href="mfa-setup.html" class="security-item"><span class="material-icons">security</span> MFA Authentication</a>
      </li>
      <li class="menu-bottom">
        <a href="#" onclick="logout()" class="logout-item"><span class="material-icons">logout</span> Logout</a>
      </li>
    </ul>
  </nav>

  <!-- Menu Overlay -->
  <div id="menu-overlay" class="menu-overlay" onclick="toggleMenu()"></div>

  <!-- Add Contact Modal -->
  <div id="add-contact-modal" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h2>Add Emergency Contact</h2>
        <button class="close-modal" onclick="closeAddContactModal()">
          <span class="material-icons">close</span>
        </button>
      </div>
      
      <form id="add-contact-form" onsubmit="addContact(event)">
        <div class="form-group">
          <label for="contact-name">Contact Name *</label>
          <input type="text" id="contact-name" name="name" required>
        </div>

        <div class="form-group">
          <label for="contact-role">Role *</label>
          <input type="text" id="contact-role" name="role" required placeholder="e.g., Veterinarian, Owner, Manager">
        </div>

        <div class="form-group">
          <label for="contact-phone">Main Phone *</label>
          <input type="tel" id="contact-phone" name="phone" required placeholder="e.g., 01234 567890">
        </div>

        <div class="form-group">
          <label for="contact-alt-phone">Alternative Phone</label>
          <input type="tel" id="contact-alt-phone" name="alt_phone" placeholder="e.g., 07123 456789">
        </div>

        <div class="form-group">
          <label for="contact-email">Email</label>
          <input type="email" id="contact-email" name="email" placeholder="<EMAIL>">
        </div>

        <div class="form-group">
          <label for="contact-priority">Priority Level *</label>
          <select id="contact-priority" name="priority_level" required>
            <option value="">Select Priority</option>
            <option value="high">High Priority</option>
            <option value="medium">Medium Priority</option>
            <option value="low">Low Priority</option>
          </select>
        </div>

        <div class="form-group">
          <label for="contact-notes">Notes</label>
          <textarea id="contact-notes" name="notes" rows="3" placeholder="Additional information about this contact..."></textarea>
        </div>

        <div class="form-group checkbox-group">
          <label class="checkbox-label">
            <input type="checkbox" id="contact-active" name="is_active" checked>
            <span class="checkmark"></span>
            Active Contact
          </label>
        </div>

        <div class="form-actions">
          <button type="cancel" class="btn-secondary" onclick="closeAddContactModal()">Cancel</button>
          <button type="submit" class="btn-primary">Add Contact</button>
        </div>
      </form>
    </div>
  </div>

  <!-- Edit Contact Modal -->
  <div id="edit-contact-modal" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h2>Edit Emergency Contact</h2>
        <button class="close-modal" onclick="closeEditContactModal()">
          <span class="material-icons">close</span>
        </button>
      </div>
      
      <form id="edit-contact-form" onsubmit="updateContact(event)">
        <!-- Form fields will be populated dynamically -->
      </form>
    </div>
  </div>

  <!-- Delete Confirmation Modal -->
  <div id="delete-contact-modal" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h2>Delete Contact</h2>
        <button class="close-modal" onclick="closeDeleteContactModal()">
          <span class="material-icons">close</span>
        </button>
      </div>
      
      <div class="modal-body">
        <p>Are you sure you want to delete this emergency contact?</p>
        <p><strong id="delete-contact-name"></strong></p>
        <p class="warning-text">This action cannot be undone.</p>
      </div>

      <div class="form-actions">
        <button type="button" class="btn-secondary" onclick="closeDeleteContactModal()">Cancel</button>
        <button type="button" class="btn-danger" onclick="confirmDeleteContact()">Delete Contact</button>
      </div>
    </div>
  </div>

  <!-- Toast Notification -->
  <div id="toast" class="toast">
    <span id="toast-message"></span>
  </div>

  <!-- Main Application Script -->
  <script type="module">
    console.log('🚀 Emergency Contacts module starting...');

    // Global function for toggling contact card expansion
    window.toggleContactCard = function(header) {
      const card = header.closest('.contact-card');
      const details = card.querySelector('.contact-details');
      const expandable = card.querySelector('.contact-expandable');

      const isExpanded = details.style.display !== 'none';

      if (isExpanded) {
        details.style.display = 'none';
        expandable.style.display = 'none';
        card.classList.remove('expanded');
      } else {
        details.style.display = 'block';
        expandable.style.display = 'block';
        card.classList.add('expanded');
      }
    };
    
    // Supabase configuration
    const SUPABASE_URL = 'https://wkclogfpyykwgjhhshsi.supabase.co';
    const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndrY2xvZ2ZweXlrd2dqaGhzaHNpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExMzc4OTQsImV4cCI6MjA2NjcxMzg5NH0.2SEQj0Gm8gZLZELn28JIipJiBkoxHelxWSqIxVYcq-k';

    // Import Supabase
    import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';
    const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

    // Global variables
    let allContacts = [];
    let currentUser = null;
    let isAdmin = false;
    let contactToDelete = null;
    let contactToEdit = null;

    // Initialize the application
    async function initializeApp() {
      try {
        console.log('🔄 Initializing Emergency Contacts...');
        
        // Check authentication
        await checkAuthentication();
        
        // Load contacts
        await loadContacts();
        
        console.log('✅ Emergency Contacts module initialized successfully');
      } catch (error) {
        console.error('❌ Error initializing app:', error);
        showToast('Error loading application. Please refresh the page.', 'error');
      }
    }

    // Check user authentication
    async function checkAuthentication() {
      try {
        const { data: { user }, error } = await supabase.auth.getUser();

        if (error || !user) {
          console.log('User not authenticated, creating anonymous session for testing');
          // For testing purposes, create an anonymous session
          try {
            const { data: anonData, error: anonError } = await supabase.auth.signInAnonymously();
            if (anonError) {
              console.error('Failed to create anonymous session:', anonError);
              showToast('Authentication required. Please contact administrator.', 'error');
              return;
            }
            currentUser = anonData.user;
            console.log('✅ Anonymous session created for testing');
          } catch (anonErr) {
            console.error('Anonymous authentication failed:', anonErr);
            showToast('Authentication error. Please try again.', 'error');
            return;
          }
        } else {
          currentUser = user;
          console.log('✅ User authenticated:', user.email);
        }

        // Check if user is admin (simplified - assume all authenticated users can manage contacts for now)
        isAdmin = true; // Simplified for testing - you can implement proper role checking later
        console.log('User role: admin (simplified), Is Admin:', isAdmin);

      } catch (error) {
        console.error('Authentication check failed:', error);
        showToast('Authentication error. Please try again.', 'error');
      }
    }

    // Load contacts from Supabase
    async function loadContacts() {
      try {
        console.log('🔄 Loading emergency contacts...');
        
        const { data: contacts, error } = await supabase
          .from('emergency_contacts')
          .select('*')
          .eq('is_active', true)
          .order('priority_level', { ascending: true })
          .order('contact_name', { ascending: true });

        if (error) {
          console.error('❌ Error loading contacts:', error);
          showToast('Error loading contacts. Please try again.', 'error');
          return;
        }

        allContacts = contacts || [];
        console.log(`✅ Loaded ${allContacts.length} contacts`);
        
        renderContacts();
      } catch (error) {
        console.error('❌ Error loading contacts:', error);
        showToast('Error loading contacts. Please try again.', 'error');
      } finally {
        // Hide loading state
        document.getElementById('loading-state').style.display = 'none';
      }
    }

    // Render contacts grouped by priority
    function renderContacts() {
      const container = document.getElementById('contacts-container');
      const emptyState = document.getElementById('empty-state');

      if (allContacts.length === 0) {
        container.style.display = 'none';
        emptyState.style.display = 'block';
        return;
      }

      container.style.display = 'block';
      emptyState.style.display = 'none';

      // Group contacts by priority
      const groupedContacts = {
        high: allContacts.filter(c => c.priority_level === 'high'),
        medium: allContacts.filter(c => c.priority_level === 'medium'),
        low: allContacts.filter(c => c.priority_level === 'low')
      };

      let html = '';

      // Render each priority group
      if (groupedContacts.high.length > 0) {
        html += renderPrioritySection('high', 'High Priority', groupedContacts.high);
      }
      if (groupedContacts.medium.length > 0) {
        html += renderPrioritySection('medium', 'Medium Priority', groupedContacts.medium);
      }
      if (groupedContacts.low.length > 0) {
        html += renderPrioritySection('low', 'Low Priority', groupedContacts.low);
      }

      container.innerHTML = html;
    }

    // Render a priority section
    function renderPrioritySection(priority, title, contacts) {
      const contactCards = contacts.map(contact => renderContactCard(contact)).join('');

      return `
        <div class="priority-section" data-priority="${priority}">
          <div class="priority-header">
            <h3 class="priority-title priority-${priority}">${title}</h3>
            <span class="contact-count">${contacts.length} contact${contacts.length !== 1 ? 's' : ''}</span>
          </div>
          <div class="contacts-grid">
            ${contactCards}
          </div>
        </div>
      `;
    }

    // Render individual contact card
    function renderContactCard(contact) {
      const altPhone = contact.alt_phone ? `
        <div class="contact-detail">
          <span class="material-icons">phone</span>
          <a href="tel:${contact.alt_phone}" class="contact-link alt-phone">${contact.alt_phone}</a>
        </div>
      ` : '';

      const email = contact.email ? `
        <div class="contact-detail">
          <span class="material-icons">email</span>
          <a href="mailto:${contact.email}" class="contact-link">${contact.email}</a>
        </div>
      ` : '';

      const notes = contact.notes ? `
        <div class="contact-notes">
          <span class="material-icons">note</span>
          <p>${contact.notes}</p>
        </div>
      ` : '';

      const adminActions = isAdmin ? `
        <div class="contact-actions">
          <button class="action-btn edit-btn" onclick="openEditContactModal(${contact.id})" title="Edit Contact">
            <span class="material-icons">edit</span>
          </button>
          <button class="action-btn delete-btn" onclick="openDeleteContactModal(${contact.id})" title="Delete Contact">
            <span class="material-icons">delete</span>
          </button>
        </div>
      ` : '';

      return `
        <div class="contact-card" data-priority="${contact.priority_level}" data-contact-id="${contact.id}">
          <div class="contact-header" onclick="toggleContactCard(this)">
            <div class="contact-info">
              <h4 class="contact-name">${contact.contact_name}</h4>
              <p class="contact-role">${contact.role}</p>
            </div>
            <div class="priority-pill priority-${contact.priority_level}">
              ${contact.priority_level.charAt(0).toUpperCase() + contact.priority_level.slice(1)}
            </div>
          </div>

          <div class="contact-details" style="display: none;">
            <div class="contact-detail primary-phone">
              <span class="material-icons">phone</span>
              <a href="tel:${contact.phone}" class="contact-link primary">${contact.phone}</a>
            </div>
            ${altPhone}
            ${email}
          </div>

          <div class="contact-expandable" style="display: none;">
            ${notes}
            ${adminActions}
          </div>
        </div>
      `;
    }



    // Filter contacts by search term
    function filterContacts() {
      const searchTerm = document.getElementById('search-input').value.toLowerCase();
      const contactCards = document.querySelectorAll('.contact-card');

      contactCards.forEach(card => {
        const name = card.querySelector('.contact-name').textContent.toLowerCase();
        const role = card.querySelector('.contact-role').textContent.toLowerCase();
        const phone = card.querySelector('.primary').textContent.toLowerCase();

        const matches = name.includes(searchTerm) ||
                       role.includes(searchTerm) ||
                       phone.includes(searchTerm);

        card.style.display = matches ? 'block' : 'none';
      });

      // Hide empty priority sections
      updatePrioritySectionVisibility();
    }

    // Filter contacts by priority
    function filterByPriority(priority) {
      // Update active filter button
      document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));
      document.querySelector(`[data-priority="${priority}"]`).classList.add('active');

      const prioritySections = document.querySelectorAll('.priority-section');

      if (priority === 'all') {
        prioritySections.forEach(section => section.style.display = 'block');
      } else {
        prioritySections.forEach(section => {
          section.style.display = section.dataset.priority === priority ? 'block' : 'none';
        });
      }
    }

    // Update priority section visibility based on visible cards
    function updatePrioritySectionVisibility() {
      const prioritySections = document.querySelectorAll('.priority-section');

      prioritySections.forEach(section => {
        const visibleCards = section.querySelectorAll('.contact-card[style*="block"], .contact-card:not([style*="none"])');
        section.style.display = visibleCards.length > 0 ? 'block' : 'none';
      });
    }

    // Emergency action functions
    function call999() {
      if (confirm('This will dial 999 emergency services. Continue?')) {
        window.location.href = 'tel:999';
      }
    }

    function notifyOwner() {
      // Find the highest priority contact or owner
      const ownerContact = allContacts.find(c =>
        c.role.toLowerCase().includes('owner') ||
        c.role.toLowerCase().includes('manager') ||
        c.priority_level === 'high'
      );

      if (ownerContact) {
        if (confirm(`Call ${ownerContact.contact_name} (${ownerContact.role})?`)) {
          window.location.href = `tel:${ownerContact.phone}`;
        }
      } else {
        showToast('No owner contact found. Please add an owner contact.', 'error');
      }
    }

    // Modal functions
    function openAddContactModal() {
      document.getElementById('add-contact-modal').style.display = 'flex';
    }

    function closeAddContactModal() {
      document.getElementById('add-contact-modal').style.display = 'none';
      document.getElementById('add-contact-form').reset();
    }

    function openEditContactModal(contactId) {
      const contact = allContacts.find(c => c.id === contactId);
      if (!contact) return;

      contactToEdit = contact;

      // Populate edit form
      const editForm = document.getElementById('edit-contact-form');
      editForm.innerHTML = `
        <div class="form-group">
          <label for="edit-contact-name">Contact Name *</label>
          <input type="text" id="edit-contact-name" name="name" value="${contact.contact_name}" required>
        </div>

        <div class="form-group">
          <label for="edit-contact-role">Role *</label>
          <input type="text" id="edit-contact-role" name="role" value="${contact.role}" required>
        </div>

        <div class="form-group">
          <label for="edit-contact-phone">Main Phone *</label>
          <input type="tel" id="edit-contact-phone" name="phone" value="${contact.phone}" required>
        </div>

        <div class="form-group">
          <label for="edit-contact-alt-phone">Alternative Phone</label>
          <input type="tel" id="edit-contact-alt-phone" name="alt_phone" value="${contact.alt_phone || ''}">
        </div>

        <div class="form-group">
          <label for="edit-contact-email">Email</label>
          <input type="email" id="edit-contact-email" name="email" value="${contact.email || ''}">
        </div>

        <div class="form-group">
          <label for="edit-contact-priority">Priority Level *</label>
          <select id="edit-contact-priority" name="priority_level" required>
            <option value="high" ${contact.priority_level === 'high' ? 'selected' : ''}>High Priority</option>
            <option value="medium" ${contact.priority_level === 'medium' ? 'selected' : ''}>Medium Priority</option>
            <option value="low" ${contact.priority_level === 'low' ? 'selected' : ''}>Low Priority</option>
          </select>
        </div>

        <div class="form-group">
          <label for="edit-contact-notes">Notes</label>
          <textarea id="edit-contact-notes" name="notes" rows="3">${contact.notes || ''}</textarea>
        </div>

        <div class="form-group checkbox-group">
          <label class="checkbox-label">
            <input type="checkbox" id="edit-contact-active" name="is_active" ${contact.is_active ? 'checked' : ''}>
            <span class="checkmark"></span>
            Active Contact
          </label>
        </div>

        <div class="form-actions">
          <button type="button" class="btn-secondary" onclick="closeEditContactModal()">Cancel</button>
          <button type="submit" class="btn-primary">Update Contact</button>
        </div>
      `;

      document.getElementById('edit-contact-modal').style.display = 'flex';
    }

    function closeEditContactModal() {
      document.getElementById('edit-contact-modal').style.display = 'none';
      contactToEdit = null;
    }

    function openDeleteContactModal(contactId) {
      const contact = allContacts.find(c => c.id === contactId);
      if (!contact) return;

      contactToDelete = contact;
      document.getElementById('delete-contact-name').textContent = `${contact.contact_name} (${contact.role})`;
      document.getElementById('delete-contact-modal').style.display = 'flex';
    }

    function closeDeleteContactModal() {
      document.getElementById('delete-contact-modal').style.display = 'none';
      contactToDelete = null;
    }

    // Add new contact
    async function addContact(event) {
      event.preventDefault();

      try {
        const formData = new FormData(event.target);
        const contactData = {
          contact_name: formData.get('name').trim(),
          role: formData.get('role').trim(),
          phone: formData.get('phone').trim(),
          alt_phone: formData.get('alt_phone')?.trim() || null,
          email: formData.get('email')?.trim() || null,
          priority_level: formData.get('priority_level'),
          notes: formData.get('notes')?.trim() || null,
          is_active: formData.get('is_active') === 'on'
        };

        // Validation
        if (!contactData.contact_name || !contactData.role || !contactData.phone || !contactData.priority_level) {
          showToast('Please fill in all required fields.', 'error');
          return;
        }

        // Phone validation
        const phoneRegex = /^[\d\s\-\+\(\)]+$/;
        if (!phoneRegex.test(contactData.phone)) {
          showToast('Please enter a valid phone number.', 'error');
          return;
        }

        // Email validation
        if (contactData.email) {
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          if (!emailRegex.test(contactData.email)) {
            showToast('Please enter a valid email address.', 'error');
            return;
          }
        }

        const { data, error } = await supabase
          .from('emergency_contacts')
          .insert([contactData])
          .select();

        if (error) {
          console.error('Error adding contact:', error);
          showToast('Error adding contact. Please try again.', 'error');
          return;
        }

        showToast('Emergency contact added successfully!', 'success');
        closeAddContactModal();
        await loadContacts();

      } catch (error) {
        console.error('Error adding contact:', error);
        showToast('Network error. Please check your connection and try again.', 'error');
      }
    }

    // Update existing contact
    async function updateContact(event) {
      event.preventDefault();

      if (!contactToEdit) return;

      try {
        const formData = new FormData(event.target);
        const contactData = {
          contact_name: formData.get('name').trim(),
          role: formData.get('role').trim(),
          phone: formData.get('phone').trim(),
          alt_phone: formData.get('alt_phone')?.trim() || null,
          email: formData.get('email')?.trim() || null,
          priority_level: formData.get('priority_level'),
          notes: formData.get('notes')?.trim() || null,
          is_active: formData.get('is_active') === 'on'
        };

        // Validation
        if (!contactData.contact_name || !contactData.role || !contactData.phone || !contactData.priority_level) {
          showToast('Please fill in all required fields.', 'error');
          return;
        }

        const { data, error } = await supabase
          .from('emergency_contacts')
          .update(contactData)
          .eq('id', contactToEdit.id)
          .select();

        if (error) {
          console.error('Error updating contact:', error);
          showToast('Error updating contact. Please try again.', 'error');
          return;
        }

        showToast('Contact updated successfully!', 'success');
        closeEditContactModal();
        await loadContacts();

      } catch (error) {
        console.error('Error updating contact:', error);
        showToast('Network error. Please check your connection and try again.', 'error');
      }
    }

    // Delete contact
    async function confirmDeleteContact() {
      if (!contactToDelete) return;

      try {
        const { error } = await supabase
          .from('emergency_contacts')
          .delete()
          .eq('id', contactToDelete.id);

        if (error) {
          console.error('Error deleting contact:', error);
          showToast('Error deleting contact. Please try again.', 'error');
          return;
        }

        showToast('Contact deleted successfully!', 'success');
        closeDeleteContactModal();
        await loadContacts();

      } catch (error) {
        console.error('Error deleting contact:', error);
        showToast('Network error. Please check your connection and try again.', 'error');
      }
    }

    // Menu toggle function
    function toggleMenu() {
      const sideMenu = document.getElementById('side-menu');
      const overlay = document.getElementById('menu-overlay');

      if (sideMenu.classList.contains('open')) {
        sideMenu.classList.remove('open');
        overlay.classList.remove('active');
      } else {
        sideMenu.classList.add('open');
        overlay.classList.add('active');
      }
    }

    // Toast notification function
    function showToast(message, type = 'success') {
      const toast = document.getElementById('toast');
      const toastMessage = document.getElementById('toast-message');

      toastMessage.textContent = message;
      toast.className = `toast ${type} show`;

      setTimeout(() => {
        toast.classList.remove('show');
      }, 3000);
    }

    // Make functions globally available
    window.toggleMenu = toggleMenu;
    window.openAddContactModal = openAddContactModal;
    window.closeAddContactModal = closeAddContactModal;
    window.openEditContactModal = openEditContactModal;
    window.closeEditContactModal = closeEditContactModal;
    window.openDeleteContactModal = openDeleteContactModal;
    window.closeDeleteContactModal = closeDeleteContactModal;
    window.addContact = addContact;
    window.updateContact = updateContact;
    window.confirmDeleteContact = confirmDeleteContact;
    window.filterContacts = filterContacts;
    window.filterByPriority = filterByPriority;
    window.call999 = call999;
    window.notifyOwner = notifyOwner;

    // Start the application
    initializeApp();
  </script>
</body>
</html>
