<!DOCTYPE html>
<html lang="en">
<head>
  <!-- REPLACE the existing viewport meta tag in ALL your HTML files with this enhanced version -->

<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes, viewport-fit=cover" />
<meta name="theme-color" content="#4285a6" />
<meta name="apple-mobile-web-app-capable" content="yes" />
<meta name="apple-mobile-web-app-status-bar-style" content="default" />
<meta name="mobile-web-app-capable" content="yes" />

<!-- Additional meta tags for better mobile support -->
<meta name="format-detection" content="telephone=no" />
<meta name="msapplication-tap-highlight" content="no" />
<meta name="apple-touch-fullscreen" content="yes" />

<!-- Prevent iOS from changing colors -->
<meta name="apple-mobile-web-app-title" content="Cornish Birds of Prey" />
<meta name="application-name" content="Cornish Birds of Prey" />

<!-- Ensure proper rendering on Windows Phone -->
<meta name="msapplication-TileColor" content="#4285a6" />
<meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <title>Donations & Adoptions – Cornish Birds of Prey</title>
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="styles.css" />
  <script src="auth-utils.js"></script>
</head>
<body>
  <div class="app-container">
    <!-- Top Navigation -->
    <header class="top-bar">
      <button class="menu-btn" onclick="toggleMenu()">
        <span class="material-icons">menu</span>
      </button>
      <h1>Donations & Adoptions</h1>
      <div class="header-actions">
        <a href="dashboard.html" class="back-link" title="Back to Dashboard">
          <span class="material-icons">arrow_back</span>
        </a>
      </div>
    </header>

    <!-- Main Content -->
    <main class="donations-adoptions-main">
      <!-- Search and Filter Section -->
      <section class="search-filter-section">
        <input class="search-bar" type="text" id="search-input" placeholder="Search by animal name or donor..." />
        
        <div class="filter-section">
          <div class="filter-group">
            <label for="type-filter">Type:</label>
            <select id="type-filter" class="filter-select">
              <option value="">All Types</option>
              <option value="adoption">Adoptions</option>
              <option value="donation">Donations</option>
            </select>
          </div>
          
          <div class="filter-group">
            <label for="status-filter">Status:</label>
            <select id="status-filter" class="filter-select">
              <option value="">All Status</option>
              <option value="Active">Active</option>
              <option value="Expiring Soon">Expiring Soon</option>
              <option value="Expired">Expired</option>
            </select>
          </div>
          
          <button class="filter-btn" onclick="clearFilters()">
            <span class="material-icons">clear</span>
            Clear Filters
          </button>
        </div>
      </section>

      <!-- Summary Cards -->
      <section class="summary-cards">
        <div class="summary-card">
          <div class="summary-icon">
            <span class="material-icons">favorite</span>
          </div>
          <div class="summary-info">
            <h3 id="total-adoptions">0</h3>
            <p>Active Adoptions</p>
          </div>
        </div>
        
        <div class="summary-card">
          <div class="summary-icon">
            <span class="material-icons">volunteer_activism</span>
          </div>
          <div class="summary-info">
            <h3 id="total-donations">0</h3>
            <p>Active Donations</p>
          </div>
        </div>
        
        <div class="summary-card">
          <div class="summary-icon">
            <span class="material-icons">attach_money</span>
          </div>
          <div class="summary-info">
            <h3 id="total-amount">£0</h3>
            <p>Total Active Amount</p>
          </div>
        </div>
        
        <div class="summary-card">
          <div class="summary-icon">
            <span class="material-icons">warning</span>
          </div>
          <div class="summary-info">
            <h3 id="expiring-soon">0</h3>
            <p>Expiring Soon</p>
          </div>
        </div>
      </section>

      <!-- Data Table -->
      <section class="table-section">
        <div class="section-header">
          <h2>Donations & Adoptions Records</h2>
          <button class="btn-primary" onclick="openAddRecordModal()">
            <span class="material-icons">add</span>
            Add Record
          </button>
        </div>
        
        <!-- Loading State -->
        <div id="loading-state" class="loading-state">
          <div class="spinner"></div>
          <p>Loading records...</p>
        </div>
        
        <!-- Table Container -->
        <div id="table-container" class="table-container" style="display: none;">
          <div class="table-wrapper scrollable-table">
            <table class="data-table">
              <thead>
                <tr>
                  <th>Animal</th>
                  <th>Donor</th>
                  <th>Type</th>
                  <th>Amount</th>
                  <th>Start Date</th>
                  <th>End Date</th>
                  <th>Status</th>
                  <th>Created By</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody id="records-tbody">
                <!-- Records will be populated here -->
              </tbody>
            </table>
          </div>
        </div>
        
        <!-- Empty State -->
        <div id="empty-state" class="empty-state" style="display: none;">
          <span class="material-icons">inbox</span>
          <h3>No records found</h3>
          <p>Try adjusting your search or filter criteria, or add a new record</p>
        </div>
      </section>
    </main>
  </div>

  <!-- Floating Action Button -->
  <button class="fab" onclick="openAddRecordModal()">
    <span class="material-icons">add</span>Add Record
  </button>

  <!-- Add Record Modal -->
  <div id="add-record-modal" class="modal">
    <div class="modal-content">
      <span class="close-modal" onclick="closeAddRecordModal()">✖</span>
      <h2>Add New Donation/Adoption Record</h2>
      <form id="add-record-form">
        <div class="form-group">
          <label for="animal-select">Animal *</label>
          <select id="animal-select" name="animal_id" required>
            <option value="">Select an animal...</option>
            <!-- Animals will be loaded here -->
          </select>
        </div>
        
        <div class="form-group">
          <label for="donor-name">Donor Name *</label>
          <input type="text" id="donor-name" name="donor_name" required placeholder="Enter donor's full name">
        </div>
        
        <div class="form-group">
          <label for="donor-email">Donor Email</label>
          <input type="email" id="donor-email" name="donor_email" placeholder="<EMAIL>">
        </div>
        
        <div class="form-group">
          <label for="donation-type">Type *</label>
          <select id="donation-type" name="donation_type" required>
            <option value="">Select type...</option>
            <option value="adoption">Adoption</option>
            <option value="donation">Donation</option>
          </select>
        </div>
        
        <div class="form-group">
          <label for="amount">Amount (£) *</label>
          <input type="number" id="amount" name="amount" required min="0.01" step="0.01" placeholder="0.00">
        </div>
        
        <div class="form-group">
          <label for="start-date">Start Date *</label>
          <input type="date" id="start-date" name="start_date" required>
        </div>
        
        <div class="form-group">
          <label for="end-date">End Date *</label>
          <input type="date" id="end-date" name="end_date" required>
        </div>
        
        <div class="form-group">
          <label for="notes">Notes</label>
          <textarea id="notes" name="notes" rows="3" placeholder="Additional notes about this donation/adoption..."></textarea>
        </div>

        <!-- User Attribution will be added here by JavaScript -->

        <div class="form-actions">
          <button type="cancel" onclick="closeAddRecordModal()">Cancel</button>
          <button type="submit" class="btn-primary">Add Record</button>
        </div>
        <div id="record-feedback" class="feedback"></div>
      </form>
    </div>
  </div>

  <!-- Side Menu -->
  <nav id="side-menu" class="side-menu">
    <div class="menu-header">
      <h2>Cornish Birds of Prey</h2>
      <button class="close-menu" onclick="toggleMenu()">
        <span class="material-icons">close</span>
      </button>
    </div>
    <ul class="menu-items">
      <li><a href="dashboard.html"><span class="material-icons">dashboard</span> Dashboard</a></li>
      <li><a href="index.html"><span class="material-icons">pets</span> Animals</a></li>
      <li><a href="staff-volunteers.html"><span class="material-icons">people</span> Staff & Volunteers</a></li>
      <li class="active"><a href="donations-adoptions.html"><span class="material-icons">volunteer_activism</span> Adoptions & Donations</a></li>
      <li><a href="documents.html"><span class="material-icons">description</span> Documents</a></li>
      <li><a href="reports.html"><span class="material-icons">bar_chart</span> Reports</a></li>
      <li><a href="emergency-contacts.html"><span class="material-icons">emergency</span> Emergency Contacts</a></li>
      <li class="menu-bottom">
        <a href="mfa-setup.html" class="security-item"><span class="material-icons">security</span> MFA Authentication</a>
      </li>
      <li class="menu-bottom">
        <a href="#" onclick="logout()" class="logout-item"><span class="material-icons">logout</span> Logout</a>
      </li>
    </ul>
  </nav>

  <!-- Menu Overlay -->
  <div id="menu-overlay" class="menu-overlay" onclick="toggleMenu()"></div>

  <!-- Main Application Script -->
  <script type="module">
    console.log('🚀 Donations & Adoptions module starting...');

    const SUPABASE_URL = 'https://wkclogfpyykwgjhhshsi.supabase.co';
    const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndrY2xvZ2ZweXlrd2dqaGhzaHNpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExMzc4OTQsImV4cCI6MjA2NjcxMzg5NH0.2SEQj0Gm8gZLZELn28JIipJiBkoxHelxWSqIxVYcq-k';

    // Import Supabase client
    const { createClient } = await import('https://cdn.skypack.dev/@supabase/supabase-js@2');
    const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

    console.log('✅ Supabase client created');

    // Global variables
    let allRecords = [];
    let filteredRecords = [];

    // Initialize the application
    async function initializeApp() {
      console.log('🔄 Initializing Donations & Adoptions module...');

      try {
        // Check authentication
        const { data: { user }, error: authError } = await supabase.auth.getUser();
        if (authError || !user) {
          console.error('❌ Authentication required');
          window.location.href = 'login.html';
          return;
        }
        console.log('✅ User authenticated:', user.email);

        // Load animals for dropdown
        await loadAnimals();

        // Load records
        await loadRecords();

        // Set up event listeners
        setupEventListeners();

        console.log('✅ Donations & Adoptions module initialized successfully');
      } catch (error) {
        console.error('❌ Error initializing app:', error);
        showToast('Error loading application. Please refresh the page.', 'error');
      }
    }

    // Load animals for dropdown
    async function loadAnimals() {
      try {
        console.log('🔄 Loading animals...');
        const { data: animals, error } = await supabase
          .from('animals')
          .select('id, name, species')
          .order('name');

        if (error) {
          console.error('❌ Error loading animals:', error);
          return;
        }

        const animalSelect = document.getElementById('animal-select');
        animalSelect.innerHTML = '<option value="">Select an animal...</option>';

        animals.forEach(animal => {
          const option = document.createElement('option');
          option.value = animal.id;
          option.textContent = `${animal.name} (${animal.species})`;
          animalSelect.appendChild(option);
        });

        console.log(`✅ Loaded ${animals.length} animals`);
      } catch (error) {
        console.error('❌ Error loading animals:', error);
      }
    }

    // Load records with animal names
    async function loadRecords() {
      try {
        console.log('🔄 Loading donation/adoption records...');

        // Show loading state
        document.getElementById('loading-state').style.display = 'flex';
        document.getElementById('table-container').style.display = 'none';
        document.getElementById('empty-state').style.display = 'none';

        const { data: records, error } = await supabase
          .from('adoptions_donations')
          .select(`
            *,
            animals (
              name,
              species
            )
          `)
          .order('created_at', { ascending: false });

        // Fetch creator information for records that have created_by
        if (records) {
          for (let record of records) {
            if (record.created_by) {
              try {
                const { data: creator, error: creatorError } = await supabase
                  .from('staff_volunteers')
                  .select('first_name, last_name, role')
                  .eq('user_id', record.created_by)
                  .single();

                if (!creatorError && creator) {
                  record.created_by_profile = creator;
                }
              } catch (err) {
                console.log('Could not fetch creator info for record:', record.id);
              }
            }
          }
        }

        if (error) {
          console.error('❌ Error loading records:', error);
          showToast('Error loading records. Please try again.', 'error');
          return;
        }

        allRecords = records || [];
        console.log(`✅ Loaded ${allRecords.length} records`);

        // Apply current filters
        applyFilters();

        // Update summary cards
        updateSummaryCards();

      } catch (error) {
        console.error('❌ Error loading records:', error);
        showToast('Error loading records. Please try again.', 'error');
      } finally {
        // Hide loading state
        document.getElementById('loading-state').style.display = 'none';
      }
    }

    // Apply filters and search
    function applyFilters() {
      const searchTerm = document.getElementById('search-input').value.toLowerCase();
      const typeFilter = document.getElementById('type-filter').value;
      const statusFilter = document.getElementById('status-filter').value;

      filteredRecords = allRecords.filter(record => {
        // Search filter
        const matchesSearch = !searchTerm ||
          record.animals?.name.toLowerCase().includes(searchTerm) ||
          record.donor_name.toLowerCase().includes(searchTerm) ||
          record.donor_email?.toLowerCase().includes(searchTerm);

        // Type filter
        const matchesType = !typeFilter || record.donation_type === typeFilter;

        // Status filter
        const matchesStatus = !statusFilter || record.status === statusFilter;

        return matchesSearch && matchesType && matchesStatus;
      });

      renderRecords();
    }

    // Render records in table
    function renderRecords() {
      const tbody = document.getElementById('records-tbody');
      const tableContainer = document.getElementById('table-container');
      const emptyState = document.getElementById('empty-state');

      if (filteredRecords.length === 0) {
        tableContainer.style.display = 'none';
        emptyState.style.display = 'flex';
        return;
      }

      emptyState.style.display = 'none';
      tableContainer.style.display = 'block';

      tbody.innerHTML = filteredRecords.map(record => {
        const animalName = record.animals?.name || 'Unknown Animal';
        const animalSpecies = record.animals?.species || '';
        const statusClass = getStatusClass(record.status);
        const typeClass = record.donation_type === 'adoption' ? 'type-adoption' : 'type-donation';

        return `
          <tr>
            <td>
              <div class="animal-info">
                <strong>${animalName}</strong>
                <small>${animalSpecies}</small>
              </div>
            </td>
            <td>
              <div class="donor-info">
                <strong>${record.donor_name}</strong>
                ${record.donor_email ? `<small>${record.donor_email}</small>` : ''}
              </div>
            </td>
            <td>
              <span class="type-badge ${typeClass}">
                ${record.donation_type.charAt(0).toUpperCase() + record.donation_type.slice(1)}
              </span>
            </td>
            <td class="amount-cell">£${parseFloat(record.amount).toFixed(2)}</td>
            <td>${formatDate(record.start_date)}</td>
            <td>${formatDate(record.end_date)}</td>
            <td>
              <span class="status-badge ${statusClass}">
                ${record.status}
              </span>
            </td>
            <td>
              <div class="creator-info">
                ${record.created_by_profile ?
                  `<strong>${record.created_by_profile.first_name} ${record.created_by_profile.last_name}</strong>
                   <small>${record.created_by_profile.role}</small>` :
                  '<small>Unknown</small>'
                }
              </div>
            </td>
            <td>
              <button class="btn-icon" onclick="viewRecord('${record.id}')" title="View Details">
                <span class="material-icons">visibility</span>
              </button>
            </td>
          </tr>
        `;
      }).join('');
    }

    // Update summary cards
    function updateSummaryCards() {
      const activeRecords = allRecords.filter(r => r.status === 'Active');
      const adoptions = activeRecords.filter(r => r.donation_type === 'adoption');
      const donations = activeRecords.filter(r => r.donation_type === 'donation');
      const expiringRecords = allRecords.filter(r => r.status === 'Expiring Soon');

      const totalAmount = activeRecords.reduce((sum, record) => sum + parseFloat(record.amount), 0);

      document.getElementById('total-adoptions').textContent = adoptions.length;
      document.getElementById('total-donations').textContent = donations.length;
      document.getElementById('total-amount').textContent = `£${totalAmount.toFixed(2)}`;
      document.getElementById('expiring-soon').textContent = expiringRecords.length;
    }

    // Utility functions
    function getStatusClass(status) {
      switch (status) {
        case 'Active': return 'status-active';
        case 'Expiring Soon': return 'status-expiring';
        case 'Expired': return 'status-expired';
        default: return '';
      }
    }

    function formatDate(dateString) {
      return new Date(dateString).toLocaleDateString('en-GB');
    }

    // Set up event listeners
    function setupEventListeners() {
      // Search input
      document.getElementById('search-input').addEventListener('input', applyFilters);

      // Filter selects
      document.getElementById('type-filter').addEventListener('change', applyFilters);
      document.getElementById('status-filter').addEventListener('change', applyFilters);

      // Form submission
      document.getElementById('add-record-form').addEventListener('submit', handleFormSubmission);

      // Set default dates
      const today = new Date().toISOString().split('T')[0];
      const nextYear = new Date();
      nextYear.setFullYear(nextYear.getFullYear() + 1);
      const nextYearDate = nextYear.toISOString().split('T')[0];

      document.getElementById('start-date').value = today;
      document.getElementById('end-date').value = nextYearDate;
    }

    // Handle form submission
    async function handleFormSubmission(e) {
      e.preventDefault();

      const formData = new FormData(e.target);
      const recordData = {
        animal_id: formData.get('animal_id'),
        donor_name: formData.get('donor_name'),
        donor_email: formData.get('donor_email') || null,
        donation_type: formData.get('donation_type'),
        amount: parseFloat(formData.get('amount')),
        start_date: formData.get('start_date'),
        end_date: formData.get('end_date'),
        notes: formData.get('notes') || null
      };

      // Validation
      if (!recordData.animal_id || !recordData.donor_name || !recordData.donation_type ||
          !recordData.amount || !recordData.start_date || !recordData.end_date) {
        document.getElementById('record-feedback').innerHTML =
          '<div class="feedback error">❌ Please fill in all required fields.</div>';
        return;
      }

      if (new Date(recordData.end_date) <= new Date(recordData.start_date)) {
        document.getElementById('record-feedback').innerHTML =
          '<div class="feedback error">❌ End date must be after start date.</div>';
        return;
      }

      try {
        console.log('🔄 Adding new record...', recordData);

        // Add user attribution
        const recordDataWithAttribution = window.authUtils.addUserAttribution(recordData, false);

        const { data, error } = await supabase
          .from('adoptions_donations')
          .insert([recordDataWithAttribution])
          .select();

        if (error) {
          console.error('❌ Error adding record:', error);
          document.getElementById('record-feedback').innerHTML =
            '<div class="feedback error">❌ Error adding record. Please try again.</div>';
          return;
        }

        console.log('✅ Record added successfully:', data);

        // Show success message
        document.getElementById('record-feedback').innerHTML =
          '<div class="feedback success">✅ Record added successfully!</div>';

        // Close modal after delay
        setTimeout(() => {
          closeAddRecordModal();
          loadRecords(); // Reload data
          showToast(`${recordData.donation_type.charAt(0).toUpperCase() + recordData.donation_type.slice(1)} record added successfully!`);
        }, 1500);

      } catch (error) {
        console.error('❌ Error adding record:', error);
        document.getElementById('record-feedback').innerHTML =
          '<div class="feedback error">❌ Error adding record. Please try again.</div>';
      }
    }

    // View record details (placeholder)
    window.viewRecord = function(recordId) {
      const record = allRecords.find(r => r.id === recordId);
      if (record) {
        alert(`Record Details:\n\nAnimal: ${record.animals?.name}\nDonor: ${record.donor_name}\nType: ${record.donation_type}\nAmount: £${record.amount}\nStatus: ${record.status}\n\nNotes: ${record.notes || 'None'}`);
      }
    };

    // Make functions globally available
    window.loadRecords = loadRecords;
    window.applyFilters = applyFilters;

    // Initialize the app when DOM is loaded
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', initializeApp);
    } else {
      initializeApp();
    }
  </script>

  <!-- Global Functions -->
  <script>
    function toggleMenu() {
      console.log('toggleMenu called');
      const sideMenu = document.getElementById('side-menu');
      const overlay = document.getElementById('menu-overlay');

      if (!sideMenu || !overlay) {
        console.error('Menu elements not found:', { sideMenu, overlay });
        return;
      }

      const isOpen = sideMenu.classList.contains('open');
      console.log('Menu is currently:', isOpen ? 'open' : 'closed');

      if (isOpen) {
        sideMenu.classList.remove('open');
        overlay.classList.remove('active');
        console.log('Menu closed');
      } else {
        sideMenu.classList.add('open');
        overlay.classList.add('active');
        console.log('Menu opened');
      }
    }

    function openAddRecordModal() {
      // Add user attribution to the form
      window.authUtils.addUserAttributionToForm('add-record-form');

      document.getElementById('add-record-modal').style.display = 'flex';
    }

    function closeAddRecordModal() {
      document.getElementById('add-record-modal').style.display = 'none';
      document.getElementById('add-record-form').reset();
      document.getElementById('record-feedback').innerHTML = '';
    }

    function clearFilters() {
      document.getElementById('search-input').value = '';
      document.getElementById('type-filter').value = '';
      document.getElementById('status-filter').value = '';
      if (window.loadRecords) window.loadRecords();
    }

    function showToast(message, type = 'success') {
      const toast = document.getElementById('toast');
      const toastMessage = document.getElementById('toast-message');

      toastMessage.textContent = message;
      toast.className = `toast ${type} show`;

      setTimeout(() => {
        toast.classList.remove('show');
      }, 3000);
    }
  </script>

  <!-- Toast Notification -->
  <div id="toast" class="toast">
    <span id="toast-message"></span>
  </div>
</body>
</html>
